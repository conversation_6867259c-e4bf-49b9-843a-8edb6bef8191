# US Golf Course Explorer

An interactive web application for exploring, planning travel to, and rating the top 100+ public golf courses in the United States.

## Project Overview

This application provides users with an interactive map interface to explore top-rated public golf courses across the US. Users can view course details, rankings, travel information, and more through an intuitive interface that combines map visualization with a list-based browsing option.

## Features

- Interactive map displaying 100+ top public golf courses in the US
- Detailed course information cards with rankings, contact details, and images
- Flight cost information from major airports
- Course ratings and reviews
- Responsive design for desktop and mobile devices
- Filter and search functionality

## Tech Stack

### Frontend
- React with TypeScript
- Tailwind CSS for styling
- shadcn/ui component library
- Leaflet for interactive maps
- Recharts for data visualization

### Backend
- Python with FastAPI
- Pydantic for data validation
- SQLModel for ORM (Object-Relational Mapping)
- PostgreSQL database (configurable)

## Project Structure

```
golf-course-explorer/
├── backend/                  # Python FastAPI backend
│   ├── venv/                 # Python virtual environment
│   ├── src/
│   │   ├── main.py           # Application entry point
│   │   ├── config/           # Configuration settings
│   │   ├── models/           # Data models
│   │   ├── routes/           # API endpoints
│   │   ├── services/         # Business logic
│   │   ├── schemas/          # Pydantic schemas
│   │   └── utils/            # Utility functions
│   ├── tests/                # Backend tests
│   ├── requirements.txt      # Python dependencies
│   └── README.md             # Backend-specific documentation
│
├── frontend/                 # React TypeScript frontend
│   ├── public/               # Static files
│   ├── src/
│   │   ├── components/       # Reusable UI components
│   │   ├── hooks/            # Custom React hooks
│   │   ├── pages/            # Application pages
│   │   ├── services/         # API service integrations
│   │   ├── types/            # TypeScript type definitions
│   │   ├── utils/            # Utility functions
│   │   ├── App.tsx           # Main application component
│   │   └── index.tsx         # Application entry point
│   ├── package.json          # Frontend dependencies
│   └── README.md             # Frontend-specific documentation
│
└── README.md                 # Main project documentation (this file)
```

## Setup and Installation

### Prerequisites

- Node.js (v16+)
- Python (v3.9+)
- npm or yarn
- PostgreSQL (optional, configurable to use SQLite)

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd golf-course-explorer/backend
   ```

2. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Run the development server:
   ```bash
   uvicorn src.main:app --reload
   ```

5. The API will be available at http://localhost:8000
   - API documentation: http://localhost:8000/docs

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd golf-course-explorer/frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

3. Start the development server:
   ```bash
   npm start
   # or
   yarn start
   ```

4. The application will be available at http://localhost:3000

## Deployment

### Backend Deployment

The backend can be deployed to any platform that supports Python applications:

1. Set up environment variables for production
2. Install production dependencies
3. Run with a production ASGI server like Uvicorn or Gunicorn

### Frontend Deployment

The frontend can be deployed to any static hosting service:

1. Build the production bundle:
   ```bash
   npm run build
   # or
   yarn build
   ```

2. Deploy the contents of the `build` directory to your hosting service

## Development Guidelines

- Follow PEP8 for Python code
- Use TypeScript for all frontend code
- Document all functions with appropriate docstrings/comments
- Keep files under 500 lines of code
- Format Python code with Black
- Use Pydantic for data validation

## License

[MIT License](LICENSE)
