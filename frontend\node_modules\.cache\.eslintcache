[{"C:\\Users\\<USER>\\golf-course-explorer\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\golf-course-explorer\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\golf-course-explorer\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\golf-course-explorer\\frontend\\src\\pages\\HomePage.tsx": "4", "C:\\Users\\<USER>\\golf-course-explorer\\frontend\\src\\services\\courseService.ts": "5", "C:\\Users\\<USER>\\golf-course-explorer\\frontend\\src\\components\\CourseDrawer.tsx": "6", "C:\\Users\\<USER>\\golf-course-explorer\\frontend\\src\\components\\MapComponent.tsx": "7", "C:\\Users\\<USER>\\golf-course-explorer\\frontend\\src\\components\\CourseCard.tsx": "8"}, {"size": 1215, "mtime": 1749206705301, "results": "9", "hashOfConfig": "10"}, {"size": 425, "mtime": 1749206705370, "results": "11", "hashOfConfig": "10"}, {"size": 398, "mtime": 1749206705213, "results": "12", "hashOfConfig": "10"}, {"size": 4846, "mtime": 1749206705613, "results": "13", "hashOfConfig": "10"}, {"size": 1931, "mtime": 1749206705473, "results": "14", "hashOfConfig": "10"}, {"size": 3692, "mtime": 1749206705545, "results": "15", "hashOfConfig": "10"}, {"size": 1860, "mtime": 1749206705510, "results": "16", "hashOfConfig": "10"}, {"size": 3697, "mtime": 1749206705580, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "tbvzyh", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\golf-course-explorer\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\golf-course-explorer\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\golf-course-explorer\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\golf-course-explorer\\frontend\\src\\pages\\HomePage.tsx", [], [], "C:\\Users\\<USER>\\golf-course-explorer\\frontend\\src\\services\\courseService.ts", [], [], "C:\\Users\\<USER>\\golf-course-explorer\\frontend\\src\\components\\CourseDrawer.tsx", [], [], "C:\\Users\\<USER>\\golf-course-explorer\\frontend\\src\\components\\MapComponent.tsx", [], [], "C:\\Users\\<USER>\\golf-course-explorer\\frontend\\src\\components\\CourseCard.tsx", [], []]